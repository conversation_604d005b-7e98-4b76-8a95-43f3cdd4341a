extends Node

const PORT = 12345
const MAX_PLAYERS = 32
@export var is_server := true
@export var player_scene: PackedScene = preload("res://scenes/PlayerCaretaker.tscn")

@onready var spawner: MultiplayerSpawner = $"../MultiplayerSpawner"

func _ready():
	if is_server:
		start_server()
	else:
		join_server("127.0.0.1")

func start_server():
	var peer = ENetMultiplayerPeer.new()
	peer.create_server(PORT, MAX_PLAYERS)
	multiplayer.multiplayer_peer = peer
	spawner.spawn_function = Callable(self, "_spawn_player")
	print("Server started on port", PORT)

func join_server(ip: String):
	var peer = ENetMultiplayerPeer.new()
	peer.create_client(ip, PORT)
	multiplayer.multiplayer_peer = peer
	spawner.spawn_function = Callable(self, "_spawn_player")
	print("Connecting to", ip, "on port", PORT)

func _spawn_player(id):
	var player = player_scene.instantiate()
	player.name = str(id)
	player.set_multiplayer_authority(id)
	return player
