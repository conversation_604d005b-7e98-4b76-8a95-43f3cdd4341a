extends CharacterBody2D

@onready var label = $Label
@onready var sprite = $Sprite2D

func _ready():
	label.text = "Player " + name

func _process(delta):
	if is_multiplayer_authority():
		var input = Vector2(
			Input.get_action_strength("ui_right") - Input.get_action_strength("ui_left"),
			Input.get_action_strength("ui_down") - Input.get_action_strength("ui_up")
		)
		velocity = input.normalized() * 50
		move_and_slide()
