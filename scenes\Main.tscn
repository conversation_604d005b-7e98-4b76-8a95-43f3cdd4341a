[gd_scene load_steps=5 format=3 uid="uid://dx7rgu2spjc06"]

[ext_resource type="Script" uid="uid://ikrrolsftr82" path="res://scripts/Network.gd" id="1_elqb8"]
[ext_resource type="Script" path="res://scripts/Alien.gd" id="2_alien"]
[ext_resource type="PackedScene" uid="uid://bvn8xqy2qxqxq" path="res://scenes/UI.tscn" id="3_ui"]
[ext_resource type="Texture2D" uid="uid://bqhqhqhqhqhqh" path="res://icon.svg" id="4_icon"]

[node name="Main" type="Node"]

[node name="Alien" type="Node2D" parent="."]
script = ExtResource("2_alien")

[node name="Sprite2D" type="Sprite2D" parent="Alien"]
modulate = Color(0.5, 1, 0.5, 1)
scale = Vector2(2, 2)
texture = ExtResource("4_icon")

[node name="CaretakerContainer" type="Node2D" parent="."]

[node name="UI" parent="." instance=ExtResource("3_ui")]

[node name="MultiplayerSpawner" type="MultiplayerSpawner" parent="."]
_spawnable_scenes = Array[PackedScene]([preload("res://scenes/PlayerCaretaker.tscn")])
spawn_path = NodePath("../CaretakerContainer")

[node name="Network" type="Node" parent="."]
script = ExtResource("1_elqb8")

[connection signal="pressed" from="UI/Button" to="Alien" method="feed"]
