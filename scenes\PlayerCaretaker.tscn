[gd_scene load_steps=4 format=3 uid="uid://6rpegh3id1uk"]

[ext_resource type="Script" uid="uid://dx3i2qnrysj2c" path="res://scripts/PlayerCaretaker.gd" id="1_ye823"]
[ext_resource type="Texture2D" uid="uid://bqhqhqhqhqhqh" path="res://icon.svg" id="2_icon"]

[sub_resource type="SceneReplicationConfig" id="SceneReplicationConfig_jvbgu"]
properties/0/path = NodePath(".:position")
properties/0/spawn = true
properties/0/sync = true

[node name="PlayerCaretaker" type="CharacterBody2D"]
script = ExtResource("1_ye823")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(1, 0.5, 0.5, 1)
texture = ExtResource("2_icon")

[node name="Label" type="Label" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -40.0
offset_right = 20.0
offset_bottom = -17.0
text = "Player"
horizontal_alignment = 1

[node name="MultiplayerSynchronizer" type="MultiplayerSynchronizer" parent="."]
replication_config = SubResource("SceneReplicationConfig_jvbgu")
