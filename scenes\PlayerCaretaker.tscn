[gd_scene load_steps=3 format=3 uid="uid://6rpegh3id1uk"]

[ext_resource type="Script" uid="uid://dx3i2qnrysj2c" path="res://scripts/PlayerCaretaker.gd" id="1_ye823"]

[sub_resource type="SceneReplicationConfig" id="SceneReplicationConfig_jvbgu"]

[node name="PlayerCaretaker" type="CharacterBody2D"]
script = ExtResource("1_ye823")

[node name="Sprite2D" type="Sprite2D" parent="."]

[node name="Label" type="Label" parent="."]
offset_right = 40.0
offset_bottom = 23.0

[node name="MultiplayerSynchronizer" type="MultiplayerSynchronizer" parent="."]
replication_config = SubResource("SceneReplicationConfig_jvbgu")
