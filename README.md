# Alien Caretaker - Multiplayer Game

A simple multiplayer game built in Godot where players can move around and take care of an alien by feeding it.

## Project Structure

```
AlienCaretaker/
├── project.godot                  ← Godot project file
├── scenes/
│   ├── Main.tscn                  ← Main game scene
│   ├── PlayerCaretaker.tscn       ← Player character scene
│   └── UI.tscn                    ← User interface scene
├── scripts/
│   ├── Network.gd                 ← Networking and multiplayer logic
│   ├── PlayerCaretaker.gd         ← Player movement and behavior
│   └── Alien.gd                   ← Alien feeding logic
└── assets/
    └── sprites/                   ← Sprite assets (if any)
```

## How to Run/Test

### Running as Server:
1. Open the project in Godot
2. In the Main scene, select the Network node
3. In the Inspector, make sure `is_server` is set to `true`
4. Run the game (F6) - this will be the server instance
5. You should see "Server started on port 12345" in the console

### Running as Client:
1. With the server running, change `is_server` to `false` on the Network node
2. Run the game again (F6) - this will be the client instance
3. You should see "Connecting to 127.0.0.1 on port 12345" in the console

## Game Features

- **Multiplayer Support**: Up to 32 players can connect
- **Player Movement**: Use arrow keys (ui_left, ui_right, ui_up, ui_down) to move around
- **Alien Care**: Click the "Feed Alien" button to increase the alien's hunger level
- **Visual Feedback**: Each player has a unique colored sprite and name label
- **Real-time Synchronization**: Player positions are synchronized across all clients

## Controls

- **Arrow Keys**: Move your character around
- **Feed Alien Button**: Click to feed the alien (increases hunger by 20 points)

## Technical Details

- **Engine**: Godot 4.4
- **Networking**: ENetMultiplayerPeer for reliable UDP networking
- **Port**: 12345 (configurable in Network.gd)
- **Max Players**: 32 (configurable in Network.gd)

## Troubleshooting

- Make sure both server and client instances are running the same version of the game
- Check that port 12345 is not blocked by firewall
- For network play across different machines, change the IP address in `join_server()` function
